# Message Framework

## Validation, Serialization, and Framework Specifications

> **Purpose**: This document defines the validation framework, serialization specifications, transformation patterns, version management, implementation guidelines, security considerations, and performance optimizations for the message schema system.

## Overview

This file contains the technical framework supporting the message schemas:
- **Validation Framework** - Multi-level validation with performance optimization
- **Serialization Specifications** - JSON, Protocol Buffers, and MessagePack support
- **Message Transformation Patterns** - Protocol adaptation and enrichment
- **Schema Version Management** - Evolution and compatibility strategies
- **Implementation Guidelines** - Code generation and testing approaches
- **Security Considerations** - Input validation and encryption support
- **Performance Optimization** - High-throughput and low-latency strategies

This framework provides the foundation for all message schemas:
- [Core message schemas](./core-message-schemas.md) for agent communication
- [Workflow message schemas](./workflow-message-schemas.md) for task management
- [System message schemas](./system-message-schemas.md) for CLI integration and monitoring

For practical implementation, see [Agent Communication](./agent-communication.md) and [Storage Patterns](./storage-patterns.md).

## 7. Validation Framework

### 7.1 Validation Rules and Levels

The framework supports multiple validation levels to balance performance and safety. These levels apply to all message types:
- [Agent communication messages](./core-message-schemas.md#agent-communication-messages)
- [Task management messages](./workflow-message-schemas.md#task-management-messages) 
- [System operation messages](./system-message-schemas.md#system-operation-messages)
- [Claude CLI integration messages](./system-message-schemas.md#claude-cli-integration-messages)

```yaml
validation_levels:
  strict:
    description: "Full schema validation with all constraints"
    performance: "Slower but comprehensive"
    use_cases: ["development", "testing", "critical_operations"]
    
  standard:
    description: "Essential validation with performance optimization"
    performance: "Balanced performance and safety"
    use_cases: ["production", "normal_operations"]
    
  permissive:
    description: "Minimal validation for high-throughput scenarios"
    performance: "Fastest validation"
    use_cases: ["high_frequency_messages", "monitoring_data"]

validation_configuration:
  schema_cache_size: 1000
  schema_cache_ttl_seconds: 3600
  max_validation_errors: 100
  fail_fast: false
  error_aggregation: true
  format_validation: true
  content_encoding_validation: true
```

### 7.2 Error Code Classification

Standardized error codes for message validation failures. These codes are used across:
- [Agent status reporting](./core-message-schemas.md#agent-status-update-message) for health monitoring
- [Task result messages](./workflow-message-schemas.md#task-result-message) for execution feedback
- [System alert messages](./system-message-schemas.md#system-alert-message) for error escalation
- [Hook response messages](./system-message-schemas.md#hook-response-message) for CLI error handling

```json
{
  "validation_errors": {
    "V1001": {
      "description": "Schema validation failed",
      "severity": "error",
      "retryable": false
    },
    "V1002": {
      "description": "Required field missing",
      "severity": "error",
      "retryable": false
    },
    "V1003": {
      "description": "Invalid field format",
      "severity": "error", 
      "retryable": false
    },
    "V1004": {
      "description": "Field value out of range",
      "severity": "error",
      "retryable": false
    },
    "V1005": {
      "description": "Invalid enum value",
      "severity": "error",
      "retryable": false
    },
    "V2001": {
      "description": "Message too large",
      "severity": "warning",
      "retryable": false
    },
    "V2002": {
      "description": "Deprecated field used",
      "severity": "warning", 
      "retryable": true
    },
    "V3001": {
      "description": "Schema version incompatible",
      "severity": "error",
      "retryable": false
    },
    "V3002": {
      "description": "Unknown message type",
      "severity": "error",
      "retryable": false
    }
  }
}
```

### 7.3 Performance Optimization

Validation performance optimizations and caching strategies. Optimizations apply to:
- High-frequency [agent status updates](./core-message-schemas.md#agent-status-update-message)
- Batch [task assignment operations](./workflow-message-schemas.md#task-assignment-message)
- Real-time [system health monitoring](./system-message-schemas.md#system-health-check-message)
- CLI [hook event processing](./system-message-schemas.md#hook-event-message)

```rust
// Example validation configuration in Rust
#[derive(Debug, Clone)]
pub struct ValidationConfig {
    pub level: ValidationLevel,
    pub schema_cache_size: usize,
    pub enable_fast_path: bool,
    pub max_validation_errors: usize,
    pub timeout_ms: u64,
}

#[derive(Debug, Clone)]
pub enum ValidationLevel {
    Strict,    // Full validation
    Standard,  // Balanced validation
    Permissive // Minimal validation
}

// Fast-path validation for high-frequency message types
pub struct FastPathValidator {
    compiled_schemas: HashMap<String, CompiledSchema>,
    performance_cache: LruCache<String, ValidationResult>,
}
```

## 8. Serialization Specifications

### 8.1 JSON Serialization Standards

JSON serialization standards apply to all framework messages, ensuring consistency across:
- [Base message envelopes](./core-message-schemas.md#base-message-envelope) and common types
- [Workflow coordination](./workflow-message-schemas.md#workflow-coordination-message) and state sync
- [System monitoring](./system-message-schemas.md#system-health-check-message) and alert data
- [CLI integration](./system-message-schemas.md#claude-cli-integration-messages) payloads

```json
{
  "json_serialization": {
    "encoding": "UTF-8",
    "date_format": "ISO 8601 (RFC 3339)",
    "number_precision": "IEEE 754 double precision",
    "string_max_length": 1048576,
    "object_max_depth": 32,
    "array_max_length": 10000,
    "null_handling": "explicit",
    "pretty_print": false,
    "escape_unicode": false
  },
  "compression": {
    "algorithm": "gzip",
    "level": 6,
    "threshold_bytes": 1024,
    "content_types": ["application/json", "text/plain"]
  },
  "security": {
    "max_string_length": 1048576,
    "max_array_length": 10000,
    "max_object_properties": 1000,
    "max_nesting_depth": 32,
    "sanitization": {
      "html_entities": true,
      "sql_injection": true,
      "xss_prevention": true
    }
  }
}
```

### 8.2 Binary Serialization Alternatives

Support for Protocol Buffers and MessagePack for performance-critical scenarios, especially:
- High-throughput [agent communication](./core-message-schemas.md#agent-communication-messages)
- Large-scale [workflow orchestration](./workflow-message-schemas.md#workflow-orchestration-messages)
- Real-time [system monitoring](./system-message-schemas.md#system-operation-messages)
- Performance-sensitive [transport layers](../transport/transport-core.md)

```protobuf
// Protocol Buffers message envelope
syntax = "proto3";
package mister_smith.messages;

message BaseMessage {
  string message_id = 1;
  int64 timestamp_unix_nano = 2;
  string schema_version = 3;
  string message_type = 4;
  string correlation_id = 5;
  string trace_id = 6;
  string source_agent_id = 7;
  string target_agent_id = 8;
  int32 priority = 9;
  string reply_to = 10;
  int64 timeout_ms = 11;
  map<string, string> metadata = 12;
  bytes payload = 13;  // Serialized message-specific payload
}
```

MessagePack configuration for space-efficient serialization:

```yaml
messagepack_config:
  use_compact_integers: true
  use_string_keys: true
  preserve_order: false
  binary_threshold_bytes: 512
  compression_after_pack: true
```

## 10. Message Transformation Patterns

### 10.1 Protocol Adaptation

Transformation rules for adapting messages between different transport protocols. Transformations support:
- [NATS subject routing](./system-message-schemas.md#nats-subject-pattern-schemas) for message distribution
- [gRPC method mapping](../transport/grpc-transport.md) for synchronous operations
- [HTTP API conversion](../transport/http-transport.md) for web integration
- Cross-protocol [agent communication](./core-message-schemas.md#agent-communication-messages)

```yaml
transformation_rules:
  json_to_protobuf:
    - map_timestamps: "ISO string to Unix nanoseconds"
    - compress_payload: "Gzip compression for large payloads"
    - validate_required: "Ensure all required protobuf fields present"
    
  protobuf_to_json:
    - expand_timestamps: "Unix nanoseconds to ISO string"
    - decompress_payload: "Decompress gzipped payloads"
    - add_schema_version: "Include JSON schema version"
    
  nats_to_grpc:
    - extract_metadata: "Move NATS headers to gRPC metadata"
    - preserve_correlation: "Maintain correlation_id across protocols"
    - map_subjects: "Convert NATS subjects to gRPC method names"
    
  grpc_to_http:
    - convert_streaming: "Buffer streaming responses for HTTP"
    - map_status_codes: "gRPC status to HTTP status codes"
    - flatten_nested: "Flatten nested objects for query parameters"
```

### 10.2 Message Enrichment

Automatic message enhancement and metadata injection. Enrichment applies to:
- [Base message envelopes](./core-message-schemas.md#base-message-envelope) for metadata completion
- [Task assignments](./workflow-message-schemas.md#task-assignment-message) for capability matching
- [System alerts](./system-message-schemas.md#system-alert-message) for context injection
- [CLI hook events](./system-message-schemas.md#hook-event-message) for session context

```json
{
  "enrichment_rules": {
    "timestamp_injection": {
      "condition": "timestamp field missing",
      "action": "Add current UTC timestamp",
      "format": "ISO 8601 RFC 3339"
    },
    "correlation_generation": {
      "condition": "correlation_id field missing and reply_to present",
      "action": "Generate UUID v4 correlation_id",
      "propagation": "Include in all related messages"
    },
    "trace_propagation": {
      "condition": "Always for cross-service calls",
      "action": "Propagate or generate trace_id",
      "sampling": "Configurable rate based on service"
    },
    "agent_metadata": {
      "condition": "Message from known agent",
      "action": "Inject agent version and capabilities",
      "source": "Agent registry lookup"
    },
    "security_context": {
      "condition": "Authenticated session",
      "action": "Add security context metadata",
      "fields": ["user_id", "permissions", "security_level"]
    }
  }
}
```

### 10.3 Content Transformation

Field mapping and data type conversion rules. Transformations enable:
- Cross-version compatibility for [schema evolution](./message-framework.md#schema-version-management)
- [Agent registration](./core-message-schemas.md#agent-registration-message) format normalization
- [Workflow state](./workflow-message-schemas.md#workflow-state-synchronization-message) format conversion
- [System health data](./system-message-schemas.md#system-health-check-message) aggregation

```yaml
field_mappings:
  version_compatibility:
    v1_0_0_to_v1_1_0:
      - add_field: "schema_version" 
        default_value: "1.1.0"
      - rename_field: 
          from: "agent_type"
          to: "agent_classification"
      - split_field:
          from: "full_name"
          to: ["first_name", "last_name"]
          
  data_type_conversions:
    timestamp_normalization:
      - from: "Unix timestamp (seconds)"
        to: "ISO 8601 string"
        conversion: "multiply by 1000, convert to datetime, format ISO"
      - from: "Unix timestamp (milliseconds)"
        to: "ISO 8601 string" 
        conversion: "convert to datetime, format ISO"
        
  nested_object_handling:
    flattening_rules:
      - condition: "target protocol is HTTP query params"
        action: "Flatten nested objects with dot notation"
        example: "metadata.source -> metadata.source"
    expansion_rules:
      - condition: "source is flattened format"
        action: "Rebuild nested object structure"
        delimiter: "."
```

## 11. Schema Version Management

### 11.1 Versioning Strategy

Versioning strategy applies to all schema families:
- [Core message schemas](./core-message-schemas.md) for foundational compatibility
- [Workflow schemas](./workflow-message-schemas.md) for task management evolution
- [System schemas](./system-message-schemas.md) for CLI and monitoring updates

```json
{
  "versioning_strategy": {
    "semantic_versioning": {
      "format": "MAJOR.MINOR.PATCH",
      "major_change": "Breaking changes requiring code updates",
      "minor_change": "Backward-compatible additions",
      "patch_change": "Bug fixes and documentation"
    },
    "compatibility_policy": {
      "backward_compatibility": "Maintain for 2 major versions",
      "forward_compatibility": "Best effort with graceful degradation",
      "deprecation_period": "6 months minimum for major changes"
    },
    "schema_evolution": {
      "additive_changes": "Always allowed in minor versions",
      "field_removal": "Deprecated in minor, removed in major",
      "type_changes": "Only allowed in major versions",
      "constraint_tightening": "Only allowed in major versions"
    }
  }
}
```

### 11.2 Compatibility Matrix

Version compatibility and migration support across all message types:
- [Agent communication](./core-message-schemas.md#agent-communication-messages) backward compatibility
- [Task management](./workflow-message-schemas.md#task-management-messages) forward compatibility
- [System operation](./system-message-schemas.md#system-operation-messages) graceful degradation
- Cross-schema compatibility validation

```yaml
compatibility_matrix:
  v1.0.0:
    compatible_with: ["1.0.x"]
    migration_from: []
    deprecation_date: "2025-12-31"
    
  v1.1.0:
    compatible_with: ["1.0.x", "1.1.x"] 
    migration_from: ["1.0.0"]
    new_features:
      - "Enhanced error details"
      - "Agent health monitoring"
      - "Workflow coordination"
      
  v2.0.0:
    compatible_with: ["2.0.x"]
    migration_from: ["1.0.0", "1.1.0"]
    breaking_changes:
      - "Renamed agent_type to agent_classification"
      - "Required correlation_id for all requests"
      - "Changed timestamp format to nanosecond precision"

migration_tools:
  schema_converter:
    supports: ["1.0.0 -> 1.1.0", "1.1.0 -> 2.0.0"]
    validation: "Pre and post conversion validation"
    rollback: "Automatic rollback on conversion failure"
    
  version_negotiation:
    strategy: "Highest common version"
    fallback: "Graceful degradation to v1.0.0"
    discovery: "Schema registry lookup"
```

### 11.3 Schema Registry

Central schema registry for version management and discovery. Registry manages:
- [Core schema definitions](./core-message-schemas.md) and common types
- [Workflow schema versions](./workflow-message-schemas.md) and compatibility
- [System schema updates](./system-message-schemas.md) and CLI integration
- Cross-reference validation and dependency tracking

```json
{
  "schema_registry": {
    "base_url": "https://schemas.mister-smith.dev",
    "endpoints": {
      "list_schemas": "/schemas",
      "get_schema": "/schemas/{schema_id}",
      "get_version": "/schemas/{schema_id}/versions/{version}",
      "validate": "/validate/{schema_id}/{version}"
    },
    "caching": {
      "cache_duration": 3600,
      "cache_size": 1000,
      "cache_key_format": "{schema_id}:{version}"
    },
    "versioning": {
      "latest_alias": "Support for 'latest' version alias",
      "version_negotiation": "Automatic version compatibility checking",
      "schema_evolution": "Track schema evolution and migrations"
    }
  }
}
```

## 12. Implementation Guidelines

### 12.1 Code Generation

Framework for generating type-safe code from schemas. Code generation supports:
- [Agent communication](./core-message-schemas.md#agent-communication-messages) type safety
- [Workflow orchestration](./workflow-message-schemas.md#workflow-orchestration-messages) validation
- [System integration](./system-message-schemas.md#claude-cli-integration-messages) type checking
- Cross-language compatibility for multi-agent systems

```rust
// Example Rust code generation
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaseMessage {
    pub message_id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub schema_version: String,
    pub message_type: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub correlation_id: Option<Uuid>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub trace_id: Option<Uuid>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub source_agent_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub target_agent_id: Option<String>,
    #[serde(default = "default_priority")]
    pub priority: u8,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reply_to: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub timeout_ms: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<std::collections::HashMap<String, String>>,
}

fn default_priority() -> u8 { 5 }

// Message type discriminated union
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "message_type")]
pub enum Message {
    #[serde(rename = "agent_command")]
    AgentCommand(AgentCommandMessage),
    #[serde(rename = "agent_status")]
    AgentStatus(AgentStatusMessage),
    #[serde(rename = "task_assignment")]
    TaskAssignment(TaskAssignmentMessage),
    // ... other message types
}
```

### 12.2 Validation Integration

Runtime validation with performance optimization. Integration patterns for:
- [Agent message validation](./core-message-schemas.md#agent-communication-messages) in communication pipelines
- [Task message validation](./workflow-message-schemas.md#task-management-messages) in workflow engines
- [System message validation](./system-message-schemas.md#system-operation-messages) in monitoring systems
- Real-time validation for [CLI integration](./system-message-schemas.md#claude-cli-integration-messages)

```rust
use jsonschema::{JSONSchema, ValidationError};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;

pub struct MessageValidator {
    schemas: HashMap<String, Arc<JSONSchema>>,
    validation_level: ValidationLevel,
}

impl MessageValidator {
    pub fn validate_message(&self, message: &Value) -> Result<(), Vec<ValidationError>> {
        let message_type = message.get("message_type")
            .and_then(|v| v.as_str())
            .ok_or_else(|| vec![ValidationError::custom("missing message_type")])?;
            
        let schema = self.schemas.get(message_type)
            .ok_or_else(|| vec![ValidationError::custom("unknown message_type")])?;
            
        match self.validation_level {
            ValidationLevel::Strict => schema.validate(message).collect(),
            ValidationLevel::Standard => self.validate_essential_fields(message),
            ValidationLevel::Permissive => self.validate_minimal(message),
        }
    }
    
    fn validate_essential_fields(&self, message: &Value) -> Result<(), Vec<ValidationError>> {
        // Optimized validation for production use
        // Only validate critical fields for performance
        Ok(())
    }
}
```

### 12.3 Testing Strategies

Comprehensive testing approach for message schemas. Testing covers:
- [Core message schema](./core-message-schemas.md) validation and serialization
- [Workflow message](./workflow-message-schemas.md) end-to-end flow testing
- [System message](./system-message-schemas.md) integration and monitoring validation
- Cross-schema compatibility and transformation testing

```yaml
testing_strategy:
  unit_tests:
    - schema_validation_tests: "Test all valid and invalid message examples"
    - serialization_tests: "Round-trip testing for all formats"
    - compatibility_tests: "Cross-version compatibility validation"
    
  integration_tests:
    - end_to_end_flow: "Complete message flow through all components"
    - protocol_conversion: "Message transformation between protocols"
    - error_handling: "Proper error propagation and handling"
    
  performance_tests:
    - validation_benchmarks: "Measure validation performance"
    - serialization_benchmarks: "Compare serialization formats"
    - memory_usage: "Monitor memory consumption patterns"
    
  property_based_tests:
    - message_generation: "Generate random valid messages"
    - invariant_testing: "Verify schema invariants hold"
    - fuzzing: "Test with malformed message data"

test_data_generation:
  valid_examples:
    - minimal_messages: "Messages with only required fields"
    - complete_messages: "Messages with all optional fields"
    - edge_cases: "Boundary value testing"
    
  invalid_examples:
    - missing_required: "Test required field validation"
    - invalid_formats: "Test format validation"
    - constraint_violations: "Test constraint enforcement"
```

## 13. Security Considerations

### 13.1 Input Validation and Sanitization

Security-focused validation rules applied to all message types:
- [Agent communication](./core-message-schemas.md#agent-communication-messages) input sanitization
- [Task data](./workflow-message-schemas.md#task-assignment-message) payload validation
- [System monitoring](./system-message-schemas.md#system-operation-messages) data integrity
- [CLI integration](./system-message-schemas.md#claude-cli-integration-messages) injection prevention

```json
{
  "security_validation": {
    "input_sanitization": {
      "max_string_length": 1048576,
      "max_array_length": 10000,
      "max_object_properties": 1000,
      "max_nesting_depth": 32,
      "prohibited_patterns": [
        "javascript:",
        "data:",
        "vbscript:",
        "<script",
        "</script>",
        "{{.*}}",
        "${.*}"
      ]
    },
    "injection_prevention": {
      "sql_injection": "Escape SQL metacharacters",
      "xss_prevention": "HTML entity encoding",
      "command_injection": "Validate against allowed characters",
      "path_traversal": "Normalize and validate file paths"
    },
    "content_validation": {
      "validate_urls": "Check URL format and allowed schemes",
      "validate_emails": "RFC 5322 email format validation",
      "validate_file_types": "Whitelist allowed file extensions",
      "validate_encoding": "Ensure UTF-8 encoding compliance"
    }
  }
}
```

### 13.2 Message Encryption and Signing

Support for end-to-end encryption and message integrity across:
- [Agent-to-agent communication](./core-message-schemas.md#agent-communication-messages) security
- [Workflow coordination](./workflow-message-schemas.md#workflow-coordination-message) protection
- [System alert](./system-message-schemas.md#system-alert-message) integrity
- [CLI session](./system-message-schemas.md#claude-cli-integration-messages) encryption

```yaml
encryption_support:
  encryption_algorithms:
    - "AES-256-GCM" # For payload encryption
    - "ChaCha20-Poly1305" # Alternative encryption
    - "RSA-OAEP-256" # For key exchange
    
  signing_algorithms:
    - "Ed25519" # Digital signatures
    - "ECDSA-P256" # Alternative signing
    - "HMAC-SHA256" # Message authentication
    
  key_management:
    rotation_policy: "Monthly automatic rotation"
    key_derivation: "PBKDF2 with 100,000 iterations"
    secure_storage: "HSM or secure enclave"
    
  message_envelope:
    encrypted_payload: "Base64-encoded encrypted content"
    signature: "Digital signature of entire message"
    key_id: "Reference to encryption key"
    algorithm: "Encryption algorithm identifier"
```

## 14. Performance Optimization

### 14.1 Message Size Optimization

Strategies for reducing message overhead across all schema types:
- [Agent status updates](./core-message-schemas.md#agent-status-update-message) compression
- [Task assignment](./workflow-message-schemas.md#task-assignment-message) payload optimization
- [System health data](./system-message-schemas.md#system-health-check-message) aggregation
- [CLI hook events](./system-message-schemas.md#hook-event-message) efficient encoding

```yaml
size_optimization:
  field_optimization:
    - use_short_field_names: "For high-frequency messages"
    - optional_field_omission: "Skip null/empty fields"
    - enum_value_compression: "Use integers instead of strings"
    
  payload_compression:
    - compression_threshold: 1024 # bytes
    - algorithms: ["gzip", "lz4", "snappy"]
    - adaptive_compression: "Choose algorithm based on payload type"
    
  schema_optimization:
    - minimize_nesting: "Flatten nested structures where possible"
    - reuse_definitions: "Extensive use of $ref for common types"
    - efficient_validation: "Optimize schema for fast validation"

binary_formats:
  protobuf_optimization:
    - field_numbering: "Optimal field number assignment"
    - packed_encoding: "Use packed encoding for arrays"
    - oneof_fields: "Use oneof for discriminated unions"
    
  messagepack_optimization:
    - compact_integers: "Use smallest integer representation"
    - string_interning: "Reuse common string values"
    - binary_data: "Use binary type for large data"
```

### 14.2 Validation Performance

High-performance validation strategies optimized for:
- High-frequency [agent communication](./core-message-schemas.md#agent-communication-messages)
- Batch [workflow operations](./workflow-message-schemas.md#workflow-orchestration-messages)
- Real-time [system monitoring](./system-message-schemas.md#system-operation-messages)
- Interactive [CLI operations](./system-message-schemas.md#claude-cli-integration-messages)

```rust
// Fast-path validation for critical message types
pub struct FastPathValidator {
    // Pre-compiled validation rules for common cases
    essential_validators: HashMap<String, Box<dyn Fn(&Value) -> bool>>,
    
    // LRU cache for validation results
    validation_cache: LruCache<u64, ValidationResult>,
    
    // Statistics for optimization
    validation_stats: ValidationStats,
}

impl FastPathValidator {
    pub fn validate_fast(&mut self, message: &Value) -> ValidationResult {
        // Fast hash-based cache lookup
        let cache_key = self.hash_message(message);
        if let Some(cached_result) = self.validation_cache.get(&cache_key) {
            self.validation_stats.cache_hits += 1;
            return cached_result.clone();
        }
        
        // Fast-path validation for known message types
        if let Some(validator) = self.essential_validators.get(message_type) {
            let result = if validator(message) {
                ValidationResult::Valid
            } else {
                ValidationResult::Invalid(vec!["fast validation failed".into()])
            };
            
            self.validation_cache.put(cache_key, result.clone());
            return result;
        }
        
        // Fall back to full schema validation
        self.validate_full_schema(message)
    }
}
```

---

## Framework Integration

### Schema Support Matrix
| Framework Component | Core Messages | Workflow Messages | System Messages |
|-------------------|---------------|-------------------|------------------|
| **Validation** | ✓ Base envelope, Agent comm | ✓ Task mgmt, Coordination | ✓ CLI hooks, Health checks |
| **Serialization** | ✓ JSON, Protobuf, MessagePack | ✓ All formats + optimization | ✓ All formats + compression |
| **Transformation** | ✓ Protocol adaptation | ✓ Workflow state mapping | ✓ CLI response formatting |
| **Versioning** | ✓ Foundation compatibility | ✓ Task evolution support | ✓ CLI integration updates |
| **Security** | ✓ Agent auth, encryption | ✓ Task data protection | ✓ System monitoring security |
| **Performance** | ✓ Agent comm optimization | ✓ Workflow batch processing | ✓ Real-time monitoring |

### Implementation Patterns
- **Agent Systems**: [Agent Communication](./agent-communication.md), [Agent Operations](./agent-operations.md)
- **Storage Systems**: [Persistence Operations](./persistence-operations.md), [Storage Patterns](./storage-patterns.md)
- **Transport Systems**: [NATS Transport](../transport/nats-transport.md), [gRPC Transport](../transport/grpc-transport.md)
- **Security Systems**: [Security Patterns](../security/security-patterns.md)

## Navigation

This file is part of the Message Schema Documentation suite:

1. [Core Message Schemas](./core-message-schemas.md) - Foundation schemas and agent communication
2. [Workflow Message Schemas](./workflow-message-schemas.md) - Task management and workflow orchestration
3. [System Message Schemas](./system-message-schemas.md) - Claude CLI integration and system operations
4. **[Message Framework](./message-framework.md)** - Validation, serialization, and framework specifications *(current file)*

### Technical Implementation
- **Validation**: [Rules](#validation-rules-and-levels), [Error Codes](#error-code-classification), [Performance](#performance-optimization-1)
- **Serialization**: [JSON Standards](#json-serialization-standards), [Binary Formats](#binary-serialization-alternatives)
- **Transformation**: [Protocol Adaptation](#protocol-adaptation), [Enrichment](#message-enrichment), [Content Mapping](#content-transformation)
- **Management**: [Versioning](#versioning-strategy), [Compatibility](#compatibility-matrix), [Registry](#schema-registry)

For the complete framework documentation, see the [Data Management Index](./CLAUDE.md).

*Message Schema Definitions v1.0.0 - Mister Smith AI Agent Framework*