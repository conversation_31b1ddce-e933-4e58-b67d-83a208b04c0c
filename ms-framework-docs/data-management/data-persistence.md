---
title: revised-data-persistence
type: note
permalink: revision-swarm/data-management/revised-data-persistence
tags:
- '#revised-document #data-persistence #foundation-focus'
---

# Data Persistence & Memory Management Architecture
## Foundation Patterns Guide

> **Canonical Reference**: See `tech-framework.md` for authoritative technology stack specifications

## Executive Summary

This document defines foundational data persistence and memory management patterns using PostgreSQL 15 with SQLx 0.7 as the primary data layer, complemented by JetStream KV for distributed state management. The architecture implements a dual-store approach with short-term state in NATS JetStream KV and long-term state in PostgreSQL, achieving high throughput while maintaining durability. Focus is on teachable patterns and basic architectural principles.

## 1. Basic Storage Architecture

### 1.1 Storage Pattern Overview

```pseudocode
DEFINE StorageLayer ENUM {
    MEMORY_CACHE,     // In-process cache
    DISTRIBUTED_KV,   // JetStream KV (short-term)
    RELATIONAL_DB,    // PostgreSQL (long-term)
    VECTOR_STORE      // Optional: for semantic search
}

INTERFACE DataStorage {
    FUNCTION store(key: String, value: Object) -> Result
    FUNCTION retrieve(key: String) -> Result<Object>
    FUNCTION remove(key: String) -> Result
    FUNCTION search(query: String) -> Result<List<Object>> // For vector stores
}
```

### 1.2 Data Categories & Two-Tier Architecture

```pseudocode
DEFINE DataType ENUM {
    SESSION_DATA,     // Temporary user sessions (KV)
    AGENT_STATE,      // Agent runtime state (KV → SQL)
    TASK_INFO,        // Task metadata (SQL)
    MESSAGE_LOG,      // Communication history (SQL)
    KNOWLEDGE_BASE    // Long-term facts (Vector + SQL)
}

CLASS DataRouter {
    FUNCTION selectStorage(dataType: DataType) -> StorageLayer {
        SWITCH dataType {
            CASE SESSION_DATA:
                RETURN DISTRIBUTED_KV  // Fast, TTL-based
            CASE AGENT_STATE:
                RETURN DISTRIBUTED_KV  // Primary, flushed to SQL
            CASE TASK_INFO:
                RETURN RELATIONAL_DB
            CASE MESSAGE_LOG:
                RETURN RELATIONAL_DB
            CASE KNOWLEDGE_BASE:
                RETURN VECTOR_STORE   // With SQL metadata
        }
    }
}
```

### 1.3 Hybrid Storage Pattern

```pseudocode
-- Dual-store implementation for agent state
CLASS HybridStateManager {
    PRIVATE kv_store: JetStreamKV
    PRIVATE sql_store: PostgresDB
    PRIVATE flush_threshold: Integer = 50
    PRIVATE dirty_keys: Set<String>
    
    FUNCTION writeState(key: String, value: Object) -> Result {
        -- Write to fast KV first
        kv_result = kv_store.put(key, value, TTL.minutes(30))
        dirty_keys.add(key)
        
        -- Trigger flush if threshold reached
        IF dirty_keys.size() >= flush_threshold THEN
            asyncFlushToSQL()
        END IF
        
        RETURN kv_result
    }
    
    FUNCTION readState(key: String) -> Result<Object> {
        -- Try fast KV first
        kv_result = kv_store.get(key)
        IF kv_result.exists() THEN
            RETURN kv_result
        END IF
        
        -- Fallback to SQL (lazy hydration)
        sql_result = sql_store.query("SELECT value FROM agent_state WHERE key = ?", key)
        IF sql_result.exists() THEN
            -- Populate KV for next access
            kv_store.put(key, sql_result.value)
            RETURN sql_result
        END IF
        
        RETURN NotFound()
    }
}
```

## 2. PostgreSQL Schema Patterns

### 2.1 Basic Schema Design with JSONB

```pseudocode
-- Core schema organization
CREATE SCHEMA agents;
CREATE SCHEMA tasks;
CREATE SCHEMA messages;

-- Enhanced agent state with JSONB
CREATE TABLE agents.state (
    agent_id UUID NOT NULL,
    key TEXT NOT NULL,
    value JSONB NOT NULL,  -- Flexible structure
    version BIGINT DEFAULT 1,
    updated_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (agent_id, key)
);

-- JSONB indexing for performance
CREATE INDEX idx_state_value_gin ON agents.state USING gin(value);
CREATE INDEX idx_state_key_btree ON agents.state(agent_id, key);
CREATE INDEX idx_state_updated ON agents.state(updated_at);

-- Task tracking with metadata
CREATE TABLE tasks.queue (
    task_id UUID PRIMARY KEY,
    task_type VARCHAR(50),
    payload JSONB,
    metadata JSONB DEFAULT '{}',  -- TTL, priority, etc.
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP  -- Optional TTL
);
```

### 2.2 State Hydration Support

```pseudocode
-- Agent checkpoint table for recovery
CREATE TABLE agents.checkpoints (
    agent_id UUID,
    checkpoint_id UUID DEFAULT gen_random_uuid(),
    state_snapshot JSONB,
    kv_revision BIGINT,  -- Track KV version
    created_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (agent_id, checkpoint_id)
);

-- Hydration query for agent startup
CREATE FUNCTION hydrate_agent_state(p_agent_id UUID) 
RETURNS TABLE(key TEXT, value JSONB) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value
    FROM agents.state s
    WHERE s.agent_id = p_agent_id
    ORDER BY s.updated_at DESC;
END;
$$ LANGUAGE plpgsql;
```

## 3. JetStream KV Patterns with TTL

### 3.1 Key-Value Store Setup with TTL

```pseudocode
CLASS KVStoreManager {
    FUNCTION createBucket(name: String, ttl_minutes: Integer = 30) -> Bucket {
        config = {
            bucket: name,
            ttl: Duration.minutes(ttl_minutes),
            replicas: 3,  -- For production
            history: 1,   -- Keep only latest
            storage: FileStorage  -- Persistent
        }
        RETURN JetStream.KeyValue.Create(config)
    }
    
    FUNCTION createTieredBuckets() -> Map<String, Bucket> {
        buckets = Map()
        -- Different TTL for different data types
        buckets["session"] = createBucket("SESSION_DATA", 60)      -- 1 hour
        buckets["agent"] = createBucket("AGENT_STATE", 30)        -- 30 min
        buckets["cache"] = createBucket("QUERY_CACHE", 5)         -- 5 min
        RETURN buckets
    }
}
```

### 3.2 State Operations with Conflict Resolution

```pseudocode
CLASS StateManager {
    PRIVATE kv_bucket: Bucket
    PRIVATE conflict_strategy: ConflictStrategy
    
    FUNCTION saveState(key: String, value: Object) -> Result {
        entry = StateEntry {
            value: value,
            timestamp: now_millis(),
            version: generateVersion()
        }
        serialized = JSON.stringify(entry)
        
        -- Optimistic concurrency control
        current = kv_bucket.get(key)
        IF current.exists() THEN
            RETURN handleConflict(key, current, entry)
        ELSE
            RETURN kv_bucket.put(key, serialized)
        END IF
    }
    
    FUNCTION handleConflict(key: String, current: Entry, new: Entry) -> Result {
        SWITCH conflict_strategy {
            CASE LAST_WRITE_WINS:
                IF new.timestamp >= current.timestamp THEN
                    RETURN kv_bucket.update(key, new, current.revision)
                END IF
            CASE VECTOR_CLOCK:
                merged = mergeWithVectorClock(current.value, new.value)
                RETURN kv_bucket.update(key, merged, current.revision)
            CASE CRDT:
                merged = crdtMerge(current.value, new.value)
                RETURN kv_bucket.update(key, merged, current.revision)
        }
    }
}
```

## 4. Common Patterns

### 4.1 Repository Pattern with Dual Store

```pseudocode
INTERFACE Repository<T> {
    FUNCTION save(entity: T) -> Result<T>
    FUNCTION find(id: UUID) -> Result<T>
    FUNCTION update(entity: T) -> Result<T>
    FUNCTION delete(id: UUID) -> Result
}

CLASS AgentRepository IMPLEMENTS Repository<Agent> {
    PRIVATE db: DatabaseConnection
    PRIVATE kv: KVBucket
    PRIVATE flush_trigger: FlushTrigger
    
    FUNCTION save(agent: Agent) -> Result<Agent> {
        -- Save to KV for immediate access
        kv_key = "agent:" + agent.id
        kv.put(kv_key, agent.serialize(), TTL.minutes(30))
        
        -- Schedule SQL flush
        flush_trigger.markDirty(agent.id)
        
        -- Async write to SQL (non-blocking)
        asyncTask {
            query = "INSERT INTO agents.registry 
                     (agent_id, agent_type, status, metadata) 
                     VALUES ($1, $2, $3, $4::jsonb)
                     ON CONFLICT (agent_id) 
                     DO UPDATE SET status = $3, metadata = $4::jsonb"
            
            db.execute(query, [
                agent.id, 
                agent.type, 
                agent.status,
                agent.metadata
            ])
        }
        
        RETURN Success(agent)
    }
    
    FUNCTION find(id: UUID) -> Result<Agent> {
        -- Try KV first (fast path)
        kv_key = "agent:" + id
        kv_result = kv.get(kv_key)
        IF kv_result.exists() THEN
            RETURN Success(Agent.deserialize(kv_result.value))
        END IF
        
        -- Fallback to SQL and hydrate KV
        query = "SELECT * FROM agents.registry WHERE agent_id = $1"
        row = db.queryOne(query, [id])
        IF row.exists() THEN
            agent = Agent.fromRow(row)
            -- Populate KV for next access
            kv.put(kv_key, agent.serialize())
            RETURN Success(agent)
        END IF
        
        RETURN NotFound()
    }
}
```

### 4.2 State Lifecycle Management

```pseudocode
CLASS StateLifecycleManager {
    PRIVATE kv: KVBucket
    PRIVATE db: DatabaseConnection
    PRIVATE dirty_tracker: Map<String, Set<String>>  -- agent_id -> keys
    
    ENUM LifecycleState {
        COLD,       -- No state loaded
        HYDRATING,  -- Loading from SQL
        ACTIVE,     -- In KV, operational
        FLUSHING,   -- Writing to SQL
        EXPIRED     -- TTL exceeded
    }
    
    FUNCTION hydrateAgent(agent_id: String) -> Result {
        setState(agent_id, HYDRATING)
        
        -- Load from SQL
        rows = db.query(
            "SELECT key, value FROM agents.state WHERE agent_id = $1",
            [agent_id]
        )
        
        -- Batch load into KV
        FOR row IN rows {
            kv_key = agent_id + ":" + row.key
            kv.put(kv_key, row.value)
        }
        
        setState(agent_id, ACTIVE)
        RETURN Success()
    }
    
    FUNCTION flushAgent(agent_id: String) -> Result {
        setState(agent_id, FLUSHING)
        dirty_keys = dirty_tracker.get(agent_id)
        
        IF dirty_keys.empty() THEN
            RETURN Success()  -- Nothing to flush
        END IF
        
        -- Begin transaction
        tx = db.beginTransaction()
        TRY {
            FOR key IN dirty_keys {
                kv_key = agent_id + ":" + key
                value = kv.get(kv_key)
                
                IF value.exists() THEN
                    tx.execute(
                        "INSERT INTO agents.state (agent_id, key, value, version) 
                         VALUES ($1, $2, $3::jsonb, $4)
                         ON CONFLICT (agent_id, key) 
                         DO UPDATE SET value = $3::jsonb, version = $4",
                        [agent_id, key, value.data, value.revision]
                    )
                END IF
            }
            
            tx.commit()
            dirty_tracker.clear(agent_id)
            setState(agent_id, ACTIVE)
            RETURN Success()
            
        } CATCH (error) {
            tx.rollback()
            setState(agent_id, ACTIVE)  -- Revert state
            RETURN Failure(error)
        }
    }
}
```

### 4.3 Enhanced Caching Pattern with TTL

```pseudocode
CLASS TieredCacheRepository {
    PRIVATE repository: Repository
    PRIVATE memory_cache: Map<UUID, CacheEntry>
    PRIVATE kv_cache: KVBucket
    PRIVATE cache_ttl: Duration
    
    STRUCT CacheEntry {
        value: Entity
        timestamp: Long
        ttl: Duration
        source: CacheSource  -- MEMORY, KV, or DB
    }
    
    FUNCTION find(id: UUID) -> Result<Entity> {
        -- L1: Check memory cache
        IF memory_cache.contains(id) THEN
            entry = memory_cache.get(id)
            IF entry.isValid() THEN
                RETURN Success(entry.value)
            ELSE
                memory_cache.remove(id)  -- Expired
            END IF
        END IF
        
        -- L2: Check KV cache
        kv_result = kv_cache.get(id.toString())
        IF kv_result.exists() THEN
            entity = deserialize(kv_result.value)
            -- Promote to memory cache
            memory_cache.put(id, CacheEntry(entity, now(), ttl, KV))
            RETURN Success(entity)
        END IF
        
        -- L3: Load from repository (SQL)
        result = repository.find(id)
        IF result.isSuccess() THEN
            -- Populate both cache layers
            kv_cache.put(id.toString(), serialize(result.value))
            memory_cache.put(id, CacheEntry(result.value, now(), ttl, DB))
        END IF
        
        RETURN result
    }
    
    FUNCTION evictExpired() {
        -- Background task to clean expired entries
        FOR entry IN memory_cache.values() {
            IF NOT entry.isValid() THEN
                memory_cache.remove(entry.id)
            END IF
        }
        -- KV entries expire automatically via TTL
    }
}
```

## 5. Advanced Connection Pool & Transaction Management

### 5.1 Enterprise Connection Pool Architecture

```pseudocode
INTERFACE ConnectionPoolCoordinator {
    create_postgres_pool(config: PostgresPoolConfig) -> PostgresPool
    create_jetstream_pool(config: JetStreamPoolConfig) -> JetStreamPool
    coordinate_transactions(operations: List<CrossSystemOperation>) -> Result
    monitor_pool_health() -> HealthStatus
    scale_pools(metrics: LoadMetrics) -> ScalingResult
}

CLASS EnterpriseConnectionManager {
    PRIVATE postgres_pools: Map<String, PostgresPool>  -- Multiple pools for different purposes
    PRIVATE jetstream_kv_pools: Map<String, JetStreamKVPool>
    PRIVATE transaction_coordinator: DistributedTransactionCoordinator
    PRIVATE connection_monitor: ConnectionHealthMonitor
    PRIVATE pool_metrics: PoolMetricsCollector
    
    STRUCT PostgresPoolConfig {
        -- Connection pool sizing (based on SQLx and Deadpool patterns)
        max_connections: Integer = 10
        min_connections: Integer = 2
        acquire_timeout: Duration = Duration.seconds(30)
        idle_timeout: Duration = Duration.minutes(10)
        max_lifetime: Duration = Duration.hours(2)
        
        -- SQLx-specific configurations
        statement_cache_capacity: Integer = 100
        test_before_acquire: Boolean = true
        
        -- Session-level configurations  
        application_name: String = "agent_system"
        statement_timeout: Duration = Duration.seconds(30)
        idle_in_transaction_timeout: Duration = Duration.seconds(60)
        
        -- Performance optimizations
        after_connect_hooks: List<SessionConfigHook>
        connection_recycling_method: RecyclingMethod = FAST
        
        -- Monitoring and alerting
        slow_query_threshold: Duration = Duration.millis(100)
        connection_leak_detection: Boolean = true
    }
    
    STRUCT JetStreamKVPoolConfig {
        max_connections: Integer = 5
        connection_timeout: Duration = Duration.seconds(10)
        kv_bucket_ttl: Duration = Duration.minutes(30)
        replicas: Integer = 3
        storage_type: StorageType = FILE_STORAGE
        max_bucket_size: Bytes = 1_GB
        history_depth: Integer = 1  -- Keep only latest values
    }
}
```

### 5.2 Connection Pool Sizing Strategies

```pseudocode
CLASS ConnectionPoolSizer {
    FUNCTION calculate_optimal_pool_size(
        agent_count: Integer,
        avg_operations_per_second: Float,
        avg_operation_duration: Duration,
        target_utilization: Float = 0.8
    ) -> PoolSizeRecommendation {
        
        -- Base calculation using Little's Law
        -- Pool Size = (Operations/sec) * (Average Duration) / Utilization
        base_size = (avg_operations_per_second * avg_operation_duration.seconds()) / target_utilization
        
        -- Adjust for agent concurrency patterns
        agent_factor = calculate_agent_concurrency_factor(agent_count)
        adjusted_size = base_size * agent_factor
        
        -- Apply bounds and safety margins
        min_safe_size = max(2, agent_count / 4)  -- At least 1 connection per 4 agents
        max_reasonable_size = min(50, agent_count * 2)  -- Cap to prevent resource exhaustion
        
        recommended_size = clamp(adjusted_size, min_safe_size, max_reasonable_size)
        
        RETURN PoolSizeRecommendation {
            recommended_size: Math.ceil(recommended_size),
            min_connections: Math.ceil(recommended_size * 0.2),
            max_connections: Math.ceil(recommended_size),
            reasoning: "Based on " + agent_count + " agents, " + avg_operations_per_second + " ops/sec"
        }
    }
    
    FUNCTION calculate_agent_concurrency_factor(agent_count: Integer) -> Float {
        -- Account for different agent types and their connection patterns
        IF agent_count <= 5 THEN
            RETURN 1.0  -- Small deployments: 1:1 ratio
        ELSE IF agent_count <= 20 THEN
            RETURN 0.8  -- Medium deployments: some connection sharing
        ELSE
            RETURN 0.6  -- Large deployments: significant connection sharing
        END IF
    }
    
    -- Environment-specific sizing templates
    FUNCTION get_environment_template(env: EnvironmentType) -> PoolSizeTemplate {
        SWITCH env {
            CASE DEVELOPMENT:
                RETURN PoolSizeTemplate {
                    postgres_max: 5,
                    postgres_min: 1,
                    jetstream_max: 2,
                    acquire_timeout: Duration.seconds(10)
                }
            CASE STAGING:
                RETURN PoolSizeTemplate {
                    postgres_max: 10,
                    postgres_min: 2,
                    jetstream_max: 5,
                    acquire_timeout: Duration.seconds(20)
                }
            CASE PRODUCTION:
                RETURN PoolSizeTemplate {
                    postgres_max: 20,
                    postgres_min: 5,
                    jetstream_max: 10,
                    acquire_timeout: Duration.seconds(30)
                }
        }
    }
}
```

### 5.3 Advanced Transaction Isolation and Boundaries

```pseudocode
CLASS AdvancedTransactionManager {
    ENUM TransactionIsolationLevel {
        READ_UNCOMMITTED,   -- Lowest isolation, fastest performance
        READ_COMMITTED,     -- Default for most operations
        REPEATABLE_READ,    -- For state flush operations and consistency requirements
        SERIALIZABLE        -- For critical updates requiring full isolation
    }
    
    ENUM TransactionBoundary {
        SINGLE_OPERATION,       -- Individual DB operation
        AGENT_TASK,            -- Complete agent task execution
        CROSS_SYSTEM,          -- Spans PostgreSQL + JetStream KV
        DISTRIBUTED_SAGA       -- Multi-agent coordination
    }
    
    CLASS TransactionContext {
        boundary: TransactionBoundary
        isolation_level: TransactionIsolationLevel
        timeout: Duration
        retry_policy: RetryPolicy
        compensation_actions: List<CompensationAction>
        correlation_id: String
        agent_id: String
    }
    
    FUNCTION execute_with_isolation(
        context: TransactionContext,
        operations: List<DatabaseOperation>
    ) -> TransactionResult {
        
        -- Select appropriate isolation level based on operation type
        isolation = determine_isolation_level(context, operations)
        
        connection = acquire_connection_for_transaction(context)
        transaction = connection.begin_transaction(isolation)
        
        -- Configure transaction settings
        configure_transaction_settings(transaction, context)
        
        TRY {
            -- Execute operations within transaction boundary
            FOR operation IN operations {
                result = operation.execute(transaction)
                
                -- Check for conflicts and adjust strategy
                IF result.has_conflict() THEN
                    conflict_resolution = handle_transaction_conflict(
                        result.conflict_type, 
                        context
                    )
                    IF conflict_resolution == ABORT_AND_RETRY THEN
                        transaction.rollback()
                        RETURN retry_with_backoff(context, operations)
                    END IF
                END IF
            }
            
            -- Pre-commit validation
            validation_result = validate_transaction_constraints(transaction, context)
            IF NOT validation_result.is_valid THEN
                transaction.rollback()
                RETURN TransactionResult.VALIDATION_FAILED(validation_result.errors)
            END IF
            
            transaction.commit()
            RETURN TransactionResult.SUCCESS
            
        } CATCH (error) {
            transaction.rollback()
            
            -- Classify error and determine retry strategy
            error_classification = classify_transaction_error(error)
            
            SWITCH error_classification {
                CASE SERIALIZATION_FAILURE:
                    RETURN retry_with_exponential_backoff(context, operations)
                CASE DEADLOCK_DETECTED:
                    RETURN retry_with_jitter(context, operations)
                CASE CONSTRAINT_VIOLATION:
                    RETURN TransactionResult.PERMANENT_FAILURE(error)
                CASE CONNECTION_FAILURE:
                    RETURN retry_with_new_connection(context, operations)
                DEFAULT:
                    RETURN TransactionResult.UNKNOWN_FAILURE(error)
            }
        }
    }
    
    FUNCTION determine_isolation_level(
        context: TransactionContext, 
        operations: List<DatabaseOperation>
    ) -> TransactionIsolationLevel {
        
        -- Override isolation level if explicitly specified
        IF context.isolation_level != NULL THEN
            RETURN context.isolation_level
        END IF
        
        -- Determine based on operation characteristics
        has_writes = operations.any(op -> op.is_write())
        has_reads = operations.any(op -> op.is_read())
        affects_shared_state = operations.any(op -> op.affects_shared_state())
        requires_consistency = context.boundary == CROSS_SYSTEM
        
        IF requires_consistency AND affects_shared_state THEN
            RETURN SERIALIZABLE  -- Strongest consistency for cross-system operations
        ELSE IF has_writes AND affects_shared_state THEN
            RETURN REPEATABLE_READ  -- Prevent phantom reads during state updates
        ELSE IF has_writes THEN
            RETURN READ_COMMITTED  -- Standard isolation for most write operations
        ELSE
            RETURN READ_COMMITTED  -- Default for read operations
        END IF
    }
    
    FUNCTION configure_transaction_settings(
        transaction: Transaction, 
        context: TransactionContext
    ) {
        -- Set transaction timeout
        transaction.execute("SET LOCAL statement_timeout = '" + context.timeout.seconds() + "s'")
        
        -- Configure based on boundary type
        SWITCH context.boundary {
            CASE AGENT_TASK:
                transaction.execute("SET LOCAL idle_in_transaction_session_timeout = '60s'")
                transaction.execute("SET LOCAL application_name = 'agent_" + context.agent_id + "'")
                
            CASE CROSS_SYSTEM:
                transaction.execute("SET LOCAL idle_in_transaction_session_timeout = '30s'")
                transaction.execute("SET LOCAL synchronous_commit = on")  -- Ensure durability
                
            CASE DISTRIBUTED_SAGA:
                transaction.execute("SET LOCAL idle_in_transaction_session_timeout = '120s'")
                transaction.execute("SET LOCAL synchronous_commit = on")
                -- Enable additional logging for saga coordination
                transaction.execute("SET LOCAL log_statement = 'all'")
        }
    }
}
```

### 5.4 Distributed Transaction Coordination

```pseudocode
CLASS DistributedTransactionCoordinator {
    PRIVATE postgres_pool: PostgresPool
    PRIVATE jetstream_kv: JetStreamKV
    PRIVATE saga_manager: SagaManager
    PRIVATE compensation_executor: CompensationExecutor
    
    FUNCTION execute_cross_system_transaction(
        postgres_operations: List<PostgresOperation>,
        jetstream_operations: List<JetStreamOperation>,
        coordination_strategy: CoordinationStrategy = SAGA_PATTERN
    ) -> DistributedTransactionResult {
        
        correlation_id = generate_correlation_id()
        
        SWITCH coordination_strategy {
            CASE TWO_PHASE_COMMIT:
                RETURN execute_two_phase_commit(postgres_operations, jetstream_operations, correlation_id)
            CASE SAGA_PATTERN:
                RETURN execute_saga_pattern(postgres_operations, jetstream_operations, correlation_id)
            CASE EVENTUAL_CONSISTENCY:
                RETURN execute_eventual_consistency(postgres_operations, jetstream_operations, correlation_id)
        }
    }
    
    FUNCTION execute_saga_pattern(
        postgres_ops: List<PostgresOperation>,
        jetstream_ops: List<JetStreamOperation>,
        correlation_id: String
    ) -> DistributedTransactionResult {
        
        saga_definition = SagaDefinition {
            correlation_id: correlation_id,
            steps: build_saga_steps(postgres_ops, jetstream_ops),
            compensation_timeout: Duration.minutes(5),
            max_retry_attempts: 3
        }
        
        saga_execution = saga_manager.start_saga(saga_definition)
        
        TRY {
            -- Step 1: Execute JetStream KV operations (fast, reversible)
            FOR jetstream_op IN jetstream_ops {
                result = execute_jetstream_operation(jetstream_op, correlation_id)
                IF result.failed() THEN
                    -- JetStream failures are typically retryable
                    retry_result = retry_jetstream_operation(jetstream_op, correlation_id)
                    IF retry_result.failed() THEN
                        RETURN initiate_saga_rollback(saga_execution, "JetStream operation failed")
                    END IF
                END IF
                
                saga_execution.mark_step_completed("jetstream_" + jetstream_op.id)
            }
            
            -- Step 2: Execute PostgreSQL operations (durable, requires careful handling)
            postgres_transaction = postgres_pool.begin_transaction(REPEATABLE_READ)
            
            TRY {
                FOR postgres_op IN postgres_ops {
                    result = postgres_op.execute(postgres_transaction)
                    saga_execution.mark_step_completed("postgres_" + postgres_op.id)
                }
                
                -- Final consistency check before commit
                consistency_check = verify_cross_system_consistency(correlation_id)
                IF NOT consistency_check.is_consistent THEN
                    postgres_transaction.rollback()
                    RETURN initiate_saga_rollback(saga_execution, "Consistency check failed")
                END IF
                
                postgres_transaction.commit()
                saga_execution.mark_completed()
                
                RETURN DistributedTransactionResult.SUCCESS(correlation_id)
                
            } CATCH (postgres_error) {
                postgres_transaction.rollback()
                RETURN initiate_saga_rollback(saga_execution, "PostgreSQL error: " + postgres_error.message)
            }
            
        } CATCH (saga_error) {
            RETURN DistributedTransactionResult.SAGA_FAILED(saga_error)
        }
    }
    
    FUNCTION initiate_saga_rollback(
        saga_execution: SagaExecution, 
        failure_reason: String
    ) -> DistributedTransactionResult {
        
        compensation_plan = build_compensation_plan(saga_execution)
        
        compensation_result = compensation_executor.execute_compensation(compensation_plan)
        
        IF compensation_result.successful() THEN
            RETURN DistributedTransactionResult.ROLLED_BACK(failure_reason)
        ELSE
            -- Compensation failed - requires manual intervention
            RETURN DistributedTransactionResult.COMPENSATION_FAILED(
                failure_reason, 
                compensation_result.errors
            )
        END IF
    }
    
    FUNCTION build_compensation_plan(saga_execution: SagaExecution) -> CompensationPlan {
        completed_steps = saga_execution.get_completed_steps()
        compensation_actions = List<CompensationAction>()
        
        -- Build compensation in reverse order
        FOR step IN completed_steps.reverse() {
            SWITCH step.type {
                CASE "jetstream_write":
                    -- JetStream KV compensation: delete or revert value
                    compensation_actions.add(JetStreamDeleteAction(step.key))
                    
                CASE "postgres_insert":
                    -- PostgreSQL compensation: delete inserted record
                    compensation_actions.add(PostgresDeleteAction(step.table, step.record_id))
                    
                CASE "postgres_update":
                    -- PostgreSQL compensation: restore previous value
                    compensation_actions.add(PostgresRestoreAction(step.table, step.record_id, step.previous_value))
            }
        }
        
        RETURN CompensationPlan {
            correlation_id: saga_execution.correlation_id,
            actions: compensation_actions,
            timeout: Duration.minutes(2),
            retry_attempts: 3
        }
    }
}
```

### 5.5 Connection Pool Health Monitoring

```pseudocode
CLASS ConnectionPoolHealthMonitor {
    PRIVATE postgres_pools: Map<String, PostgresPool>
    PRIVATE jetstream_pools: Map<String, JetStreamKVPool>
    PRIVATE health_metrics: HealthMetricsCollector
    PRIVATE alert_manager: AlertManager
    
    STRUCT PoolHealthMetrics {
        pool_name: String
        pool_type: PoolType
        total_connections: Integer
        active_connections: Integer
        idle_connections: Integer
        pending_acquisitions: Integer
        acquisition_time_p95: Duration
        connection_errors: Counter
        health_check_success_rate: Float
        last_health_check: Timestamp
    }
    
    FUNCTION monitor_all_pools() {
        postgres_metrics = collect_postgres_metrics()
        jetstream_metrics = collect_jetstream_metrics()
        
        -- Analyze metrics and trigger alerts
        analyze_pool_health(postgres_metrics)
        analyze_pool_health(jetstream_metrics)
        
        -- Update health status
        update_overall_health_status(postgres_metrics, jetstream_metrics)
    }
    
    FUNCTION collect_postgres_metrics() -> List<PoolHealthMetrics> {
        metrics = List<PoolHealthMetrics>()
        
        FOR pool_name, pool IN postgres_pools {
            pool_metrics = PoolHealthMetrics {
                pool_name: pool_name,
                pool_type: POSTGRES,
                total_connections: pool.size(),
                active_connections: pool.active_count(),
                idle_connections: pool.idle_count(),
                pending_acquisitions: pool.pending_count(),
                acquisition_time_p95: pool.acquisition_time_percentile(95),
                connection_errors: pool.error_count(),
                health_check_success_rate: calculate_health_success_rate(pool),
                last_health_check: now()
            }
            
            metrics.add(pool_metrics)
        }
        
        RETURN metrics
    }
    
    FUNCTION analyze_pool_health(metrics: List<PoolHealthMetrics>) {
        FOR metric IN metrics {
            -- Check pool utilization
            utilization = metric.active_connections / metric.total_connections
            IF utilization > 0.9 THEN
                alert_manager.trigger_alert(AlertType.HIGH_POOL_UTILIZATION, {
                    pool_name: metric.pool_name,
                    utilization: utilization,
                    severity: HIGH
                })
            END IF
            
            -- Check acquisition time
            IF metric.acquisition_time_p95 > Duration.seconds(5) THEN
                alert_manager.trigger_alert(AlertType.SLOW_CONNECTION_ACQUISITION, {
                    pool_name: metric.pool_name,
                    p95_time: metric.acquisition_time_p95,
                    severity: MEDIUM
                })
            END IF
            
            -- Check connection errors
            error_rate = metric.connection_errors / (metric.active_connections + 1)
            IF error_rate > 0.05 THEN
                alert_manager.trigger_alert(AlertType.HIGH_CONNECTION_ERROR_RATE, {
                    pool_name: metric.pool_name,
                    error_rate: error_rate,
                    severity: HIGH
                })
            END IF
            
            -- Check health success rate
            IF metric.health_check_success_rate < 0.95 THEN
                alert_manager.trigger_alert(AlertType.HEALTH_CHECK_FAILURES, {
                    pool_name: metric.pool_name,
                    success_rate: metric.health_check_success_rate,
                    severity: CRITICAL
                })
            END IF
        }
    }
    
    FUNCTION perform_health_checks() {
        -- PostgreSQL health checks
        FOR pool_name, pool IN postgres_pools {
            health_result = check_postgres_pool_health(pool)
            health_metrics.record_health_check(pool_name, POSTGRES, health_result)
        }
        
        -- JetStream KV health checks
        FOR pool_name, pool IN jetstream_pools {
            health_result = check_jetstream_pool_health(pool)
            health_metrics.record_health_check(pool_name, JETSTREAM_KV, health_result)
        }
    }
    
    FUNCTION check_postgres_pool_health(pool: PostgresPool) -> HealthCheckResult {
        TRY {
            connection = pool.acquire_timeout(Duration.seconds(5))
            
            start_time = now()
            result = connection.execute("SELECT 1 as health_check")
            latency = now() - start_time
            
            pool.release(connection)
            
            IF latency > Duration.millis(100) THEN
                RETURN HealthCheckResult.DEGRADED(latency)
            ELSE
                RETURN HealthCheckResult.HEALTHY(latency)
            END IF
            
        } CATCH (timeout_error) {
            RETURN HealthCheckResult.UNHEALTHY("Connection acquisition timeout")
        } CATCH (query_error) {
            RETURN HealthCheckResult.UNHEALTHY("Query execution failed: " + query_error.message)
        }
    }
    
    FUNCTION check_jetstream_pool_health(pool: JetStreamKVPool) -> HealthCheckResult {
        TRY {
            kv_connection = pool.acquire()
            
            start_time = now()
            -- Perform a lightweight operation
            kv_info = kv_connection.get_bucket_info()
            latency = now() - start_time
            
            pool.release(kv_connection)
            
            IF latency > Duration.millis(50) THEN
                RETURN HealthCheckResult.DEGRADED(latency)
            ELSE
                RETURN HealthCheckResult.HEALTHY(latency)
            END IF
            
        } CATCH (error) {
            RETURN HealthCheckResult.UNHEALTHY("JetStream KV error: " + error.message)
        }
    }
}
```

### 5.6 Connection String Templates and Configuration Management

```pseudocode
CLASS DataLayerConfigurationManager {
    FUNCTION build_postgres_connection_string(env: Environment) -> String {
        config = load_postgres_config(env)
        
        -- Support various connection formats based on environment
        SWITCH env.deployment_type {
            CASE LOCAL_DEVELOPMENT:
                RETURN build_local_postgres_url(config)
            CASE DOCKER_COMPOSE:
                RETURN build_docker_postgres_url(config)
            CASE KUBERNETES:
                RETURN build_k8s_postgres_url(config)
            CASE CLOUD_MANAGED:
                RETURN build_cloud_postgres_url(config)
        }
    }
    
    FUNCTION build_local_postgres_url(config: PostgresConfig) -> String {
        -- Local development with Unix sockets or localhost
        IF config.use_unix_socket THEN
            socket_path = url_encode(config.socket_path)
            RETURN "postgres://" + socket_path + "/" + config.database + 
                   "?application_name=" + config.application_name
        ELSE
            RETURN "postgres://" + config.username + ":" + config.password + 
                   "@localhost:" + config.port + "/" + config.database +
                   "?application_name=" + config.application_name + 
                   "&sslmode=disable"
        END IF
    }
    
    FUNCTION build_cloud_postgres_url(config: PostgresConfig) -> String {
        -- Cloud deployment with SSL and connection pooling
        RETURN "postgres://" + config.username + ":" + config.password + 
               "@" + config.host + ":" + config.port + "/" + config.database +
               "?application_name=" + config.application_name +
               "&sslmode=require" +
               "&sslrootcert=" + config.ssl_root_cert +
               "&connect_timeout=" + config.connect_timeout.seconds() +
               "&statement_timeout=" + config.statement_timeout.seconds()
    }
    
    FUNCTION load_postgres_config(env: Environment) -> PostgresConfig {
        RETURN PostgresConfig {
            host: env.get("PG_HOST", "localhost"),
            port: env.get_int("PG_PORT", 5432),
            database: env.get("PG_DATABASE", "agent_system"),
            username: env.get("PG_USER", "postgres"),
            password: env.get("PG_PASSWORD", ""),
            application_name: env.get("PG_APP_NAME", "agent_data_layer"),
            socket_path: env.get("PG_SOCKET_PATH", "/var/run/postgresql"),
            use_unix_socket: env.get_bool("PG_USE_SOCKET", false),
            ssl_root_cert: env.get("PG_SSL_ROOT_CERT", ""),
            connect_timeout: Duration.seconds(env.get_int("PG_CONNECT_TIMEOUT", 10)),
            statement_timeout: Duration.seconds(env.get_int("PG_STATEMENT_TIMEOUT", 30)),
            max_connections: env.get_int("PG_MAX_CONNECTIONS", 10),
            min_connections: env.get_int("PG_MIN_CONNECTIONS", 2)
        }
    }
    
    FUNCTION build_jetstream_kv_config(env: Environment) -> JetStreamKVConfig {
        RETURN JetStreamKVConfig {
            servers: env.get_list("NATS_SERVERS", ["nats://localhost:4222"]),
            credentials_file: env.get("NATS_CREDS_FILE", ""),
            tls_cert: env.get("NATS_TLS_CERT", ""),
            tls_key: env.get("NATS_TLS_KEY", ""),
            ca_cert: env.get("NATS_CA_CERT", ""),
            bucket_prefix: env.get("NATS_KV_PREFIX", "agent_"),
            default_ttl: Duration.minutes(env.get_int("NATS_KV_TTL_MINUTES", 30)),
            replicas: env.get_int("NATS_KV_REPLICAS", 3),
            max_bucket_size: parse_bytes(env.get("NATS_KV_MAX_SIZE", "1GB")),
            compression: env.get_bool("NATS_KV_COMPRESSION", true)
        }
    }
}
```

## 6. Error Handling and Conflict Resolution

### 6.1 Enhanced Error Types

```pseudocode
ENUM DataError {
    NOT_FOUND,
    DUPLICATE_KEY,
    CONNECTION_FAILED,
    SERIALIZATION_ERROR,
    VERSION_CONFLICT,    -- For optimistic locking
    TTL_EXPIRED,        -- KV entry expired
    CONSISTENCY_VIOLATION
}

CLASS DataResult<T> {
    value: T?
    error: DataError?
    metadata: ResultMetadata?  -- Version, timestamp, etc.
    
    FUNCTION isSuccess() -> Boolean
    FUNCTION getValue() -> T
    FUNCTION getError() -> DataError
    FUNCTION requiresRetry() -> Boolean {
        RETURN error IN [VERSION_CONFLICT, SERIALIZATION_ERROR]
    }
}
```

### 6.2 Conflict Resolution Strategies

```pseudocode
CLASS ConflictResolver {
    ENUM Strategy {
        LAST_WRITE_WINS,
        VECTOR_CLOCK,
        CRDT_MERGE,
        CUSTOM_MERGE
    }
    
    FUNCTION resolve(
        current: StateEntry, 
        incoming: StateEntry, 
        strategy: Strategy
    ) -> StateEntry {
        SWITCH strategy {
            CASE LAST_WRITE_WINS:
                RETURN (incoming.timestamp > current.timestamp) ? 
                       incoming : current
                       
            CASE VECTOR_CLOCK:
                IF vectorClockDominates(incoming.clock, current.clock) THEN
                    RETURN incoming
                ELSE IF vectorClockDominates(current.clock, incoming.clock) THEN
                    RETURN current
                ELSE
                    -- Concurrent, need merge
                    RETURN mergeStates(current, incoming)
                END IF
                
            CASE CRDT_MERGE:
                RETURN StateEntry {
                    value: crdtMerge(current.value, incoming.value),
                    timestamp: max(current.timestamp, incoming.timestamp),
                    version: max(current.version, incoming.version) + 1
                }
        }
    }
}
```

### 6.3 Retry Logic with Backoff

```pseudocode
CLASS RetryHandler {
    FUNCTION withRetry(
        operation: Function, 
        maxAttempts: Integer = 3,
        backoffBase: Duration = 100ms
    ) -> Result {
        attempts = 0
        
        WHILE attempts < maxAttempts {
            result = operation()
            
            IF result.isSuccess() THEN
                RETURN result
            END IF
            
            IF NOT result.requiresRetry() THEN
                RETURN result  -- Non-retryable error
            END IF
            
            attempts += 1
            IF attempts < maxAttempts THEN
                backoff = backoffBase * (2 ^ attempts)  -- Exponential
                sleep(min(backoff, Duration.seconds(5)))  -- Cap at 5s
            END IF
        }
        
        RETURN Failure("Max retry attempts reached")
    }
}
```

## 7. Basic Monitoring

### 7.1 Consistency Metrics

```pseudocode
CLASS ConsistencyMonitor {
    PRIVATE metrics: MetricsCollector
    
    FUNCTION trackConsistencyWindow(agent_id: String) {
        -- Measure time between KV write and SQL flush
        kv_time = getLastKVWrite(agent_id)
        sql_time = getLastSQLWrite(agent_id)
        lag = sql_time - kv_time
        
        metrics.recordGauge("consistency_lag_ms", lag, {"agent": agent_id})
        
        IF lag > Duration.millis(200) THEN
            metrics.incrementCounter("consistency_violations")
            triggerRepairJob(agent_id)
        END IF
    }
    
    FUNCTION getMemoryStats() -> MemoryStats {
        RETURN MemoryStats {
            kv_entries: countKVEntries(),
            sql_rows: countSQLRows(),
            dirty_entries: countDirtyEntries(),
            avg_flush_time: metrics.getAverage("flush_duration_ms"),
            consistency_window_p95: metrics.getPercentile("consistency_lag_ms", 95)
        }
    }
}
```

### 7.2 Health Checks

```pseudocode
CLASS HealthChecker {
    FUNCTION checkDatabase() -> HealthStatus {
        TRY {
            start = now()
            db.execute("SELECT 1")
            latency = now() - start
            
            RETURN HealthStatus {
                status: (latency < 10ms) ? HEALTHY : DEGRADED,
                latency: latency,
                details: "Database responding"
            }
        } CATCH (error) {
            RETURN HealthStatus.UNHEALTHY
        }
    }
    
    FUNCTION checkJetStream() -> HealthStatus {
        TRY {
            jetstream.ping()
            -- Check KV bucket status
            bucket_info = jetstream.getBucketInfo("AGENT_STATE")
            
            RETURN HealthStatus {
                status: HEALTHY,
                details: {
                    entries: bucket_info.entry_count,
                    bytes: bucket_info.bytes,
                    ttl_config: bucket_info.ttl
                }
            }
        } CATCH (error) {
            RETURN HealthStatus.UNHEALTHY
        }
    }
    
    FUNCTION checkConsistency() -> HealthStatus {
        stats = ConsistencyMonitor.getMemoryStats()
        IF stats.consistency_window_p95 > 200 THEN
            RETURN HealthStatus.DEGRADED
        ELSE
            RETURN HealthStatus.HEALTHY
        END IF
    }
}
```

## Summary

This document provides foundational data persistence patterns focusing on:

1. **Dual-store architecture** - Fast KV for working state, durable SQL for long-term
2. **PostgreSQL patterns** - JSONB for flexibility, proper indexing, hydration support
3. **JetStream KV usage** - TTL-based expiry, optimistic concurrency
4. **State lifecycle** - Hydration on startup, periodic flushes, consistency tracking
5. **Conflict resolution** - Multiple strategies from simple LWW to CRDT merging
6. **Connection management** - Pooling, transactions with appropriate isolation
7. **Error handling** - Retries, conflict detection, consistency violations
8. **Monitoring** - Health checks, consistency windows, performance metrics

These patterns form the foundation for high-performance data management in distributed agent systems, balancing speed with durability while maintaining eventual consistency within tight time bounds.