GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    base64 (0.3.0)
    bigdecimal (3.2.2)
    colorator (1.1.0)
    concurrent-ruby (1.3.5)
    csv (3.3.5)
    em-websocket (0.5.3)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0)
    eventmachine (1.2.7)
    ffi (1.17.2)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86-linux-gnu)
    ffi (1.17.2-x86-linux-musl)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    forwardable-extended (2.6.0)
    google-protobuf (4.31.1)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-aarch64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-aarch64-linux-musl)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86-linux-musl)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-linux-musl)
      bigdecimal
      rake (>= 13)
    http_parser.rb (0.8.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    jekyll (4.4.1)
      addressable (~> 2.4)
      base64 (~> 0.2)
      colorator (~> 1.0)
      csv (~> 3.0)
      em-websocket (~> 0.5)
      i18n (~> 1.0)
      jekyll-sass-converter (>= 2.0, < 4.0)
      jekyll-watch (~> 2.0)
      json (~> 2.6)
      kramdown (~> 2.3, >= 2.3.1)
      kramdown-parser-gfm (~> 1.0)
      liquid (~> 4.0)
      mercenary (~> 0.3, >= 0.3.6)
      pathutil (~> 0.9)
      rouge (>= 3.0, < 5.0)
      safe_yaml (~> 1.0)
      terminal-table (>= 1.8, < 4.0)
      webrick (~> 1.7)
    jekyll-feed (0.17.0)
      jekyll (>= 3.7, < 5.0)
    jekyll-sass-converter (3.1.0)
      sass-embedded (~> 1.75)
    jekyll-seo-tag (2.8.0)
      jekyll (>= 3.8, < 5.0)
    jekyll-sitemap (1.4.0)
      jekyll (>= 3.7, < 5.0)
    jekyll-watch (2.2.1)
      listen (~> 3.0)
    json (2.12.2)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    liquid (4.0.4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    mercenary (0.4.0)
    pathutil (0.16.2)
      forwardable-extended (~> 2.6)
    public_suffix (6.0.2)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rexml (3.4.1)
    rouge (4.5.2)
    safe_yaml (1.0.5)
    sass-embedded (1.89.2)
      google-protobuf (~> 4.31)
      rake (>= 13)
    sass-embedded (1.89.2-aarch64-linux-android)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-aarch64-linux-gnu)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-aarch64-linux-musl)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-arm-linux-androideabi)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-arm-linux-gnueabihf)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-arm-linux-musleabihf)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-arm64-darwin)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-riscv64-linux-android)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-riscv64-linux-gnu)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-riscv64-linux-musl)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-x86_64-darwin)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-x86_64-linux-android)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-x86_64-linux-gnu)
      google-protobuf (~> 4.31)
    sass-embedded (1.89.2-x86_64-linux-musl)
      google-protobuf (~> 4.31)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    unicode-display_width (2.6.0)
    webrick (1.9.1)

PLATFORMS
  aarch64-linux-android
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-androideabi
  arm-linux-gnu
  arm-linux-gnueabihf
  arm-linux-musl
  arm-linux-musleabihf
  arm64-darwin
  riscv64-linux-android
  riscv64-linux-gnu
  riscv64-linux-musl
  ruby
  x86-linux-gnu
  x86-linux-musl
  x86_64-darwin
  x86_64-linux-android
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  jekyll (~> 4.4)
  jekyll-feed (~> 0.17.0)
  jekyll-seo-tag (~> 2.8)
  jekyll-sitemap (~> 1.4)

BUNDLED WITH
   2.6.9
