I"	{"source" => "/Users/<USER>/<PERSON>-<PERSON>/<PERSON>-<PERSON>/jekyll-site", "destination" => "/Users/<USER>/<PERSON>-<PERSON>/<PERSON>-<PERSON>/jekyll-site/_site", "collections_dir" => "", "cache_dir" => ".jekyll-cache", "plugins_dir" => "_plugins", "layouts_dir" => "_layouts", "data_dir" => "_data", "includes_dir" => "_includes", "collections" => {"posts" => {"output" => true, "permalink" => "/:categories/:title/"}, "core_architecture" => {"output" => true, "permalink" => "/:collection/:name/"}, "data_management" => {"output" => true, "permalink" => "/:collection/:name/"}, "operations" => {"output" => true, "permalink" => "/:collection/:name/"}, "research" => {"output" => true, "permalink" => "/:collection/:name/"}, "security" => {"output" => true, "permalink" => "/:collection/:name/"}, "transport" => {"output" => true, "permalink" => "/:collection/:name/"}}, "safe" => false, "include" => [".htaccess"], "exclude" => [".sass-cache", ".jekyll-cache", "gemfiles", "Gemfile", "Gemfile.lock", "node_modules", "vendor/bundle/", "vendor/cache/", "vendor/gems/", "vendor/ruby/"], "keep_files" => [".git", ".svn"], "encoding" => "utf-8", "markdown_ext" => "markdown,mkdown,mkdn,mkd,md", "strict_front_matter" => false, "show_drafts" => nil, "limit_posts" => 0, "future" => false, "unpublished" => false, "whitelist" => [], "plugins" => ["jekyll-feed", "jekyll-sitemap", "jekyll-seo-tag"], "markdown" => "kramdown", "highlighter" => "rouge", "lsi" => false, "excerpt_separator" => "\n\n", "incremental" => false, "detach" => false, "port" => "4001", "host" => "127.0.0.1", "baseurl" => "/Mister-Smith", "show_dir_listing" => false, "permalink" => "/:categories/:title/", "paginate_path" => "/page:num", "timezone" => nil, "quiet" => false, "verbose" => false, "defaults" => [], "liquid" => {"error_mode" => "warn", "strict_filters" => false, "strict_variables" => false}, "kramdown" => {"auto_ids" => true, "toc_levels" => [1, 2, 3, 4, 5, 6], "entity_output" => "as_char", "smart_quotes" => "lsquo,rsquo,ldquo,rdquo", "input" => "GFM", "hard_wrap" => false, "guess_lang" => true, "footnote_nr" => 1, "show_warnings" => false}, "title" => "Mister Smith AI Agent Framework", "description" => "Multi-agent orchestration framework built with Rust", "url" => "http://localhost:4001", "serving" => true, "watch" => true}:ET